<template>
  <div class="address-form">
    <div class="address-form__header">
      <h2 class="address-form__title">{{ isEditMode ? '编辑地址' : '新增地址' }}</h2>
      <p class="address-form__subtitle">请填写准确的收货信息</p>
    </div>

    <div class="address-form__content">
      <AddressForm
        ref="addressFormRef"
        :initial-data="initialAddressData"
        @validate="onFormValidate"
        @region-change="onRegionChange"
      />
    </div>

    <AddressActionBar size="xlarge" @add="saveAddress" class="address-form__actions">
      <WoButton
        size="xlarge"
        type="primary"
        block
        :loading="isSubmitting"
        :disabled="!isFormValid"
        @click="saveAddress"
      >
        {{ isSubmitting ? '保存中...' : '保存地址' }}
      </WoButton>
    </AddressActionBar>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import AddressForm from '@components/Address/AddressForm.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { addAddr, editAddr, queryUserAddrList } from '@api/interface/address.js'
import base64 from '@utils/base64.js'

// 路由实例
const router = useRouter()
const route = useRoute()

// 状态管理
const isSubmitting = ref(false)
const isFormValid = ref(false)
const initialAddressData = ref({})

// 组件引用
const addressFormRef = ref(null)

// 计算属性
const isEditMode = computed(() => !!route.query.addrId)

// 表单验证回调
const onFormValidate = ({ isValid }) => {
  isFormValid.value = isValid
}

// 地区变化回调
const onRegionChange = (regionData) => {
  // 可以在这里处理地区变化的逻辑
  console.log('地区变化:', regionData)
}

// 构建请求参数
const buildRequestParams = () => {
  if (!addressFormRef.value) return {}

  const params = addressFormRef.value.buildRequestParams()
  // 对姓名进行base64编码
  return {
    ...params,
    recName: base64(params.recName)
  }
}

// 保存地址信息
const saveAddress = debounce(async () => {
  if (isSubmitting.value) return

  // 表单验证
  if (!addressFormRef.value?.validateForm()) return

  isSubmitting.value = true
  showLoadingToast()

  try {
    const baseParams = buildRequestParams()
    const addrId = route.query.addrId

    let result
    if (isEditMode.value) {
      // 编辑模式
      result = await editAddr({
        ...baseParams,
        addressId: addrId
      })
    } else {
      // 新增模式
      result = await addAddr(baseParams)
    }

    const [err] = result

    if (err) {
      showToast(err.msg || `${isEditMode.value ? '保存' : '新增'}失败`)
      return
    }

    showToast(`${isEditMode.value ? '保存' : '新增'}成功`)

    // 延迟返回，让用户看到成功提示
    setTimeout(() => {
      router.back()
    }, 1500)

  } catch (error) {
    console.error('保存地址失败:', error)
    showToast(error.message || `${isEditMode.value ? '保存' : '新增'}失败`)
  } finally {
    closeToast()
    isSubmitting.value = false
  }
}, 300)

// 初始化编辑模式数据
const initEditModeData = async () => {
  const addrId = route.query.addrId
  if (!addrId) return

  // 优先使用路由参数中的地址数据
  if (route.params.address) {
    initialAddressData.value = route.params.address
    return
  }

  // 通过API获取地址数据
  const [err, addrList] = await queryUserAddrList()
  if (err || !addrList?.length) {
    showToast(err?.msg || '获取地址信息失败')
    return
  }

  const matchedAddr = addrList.find(item => item.addressId === addrId)
  if (matchedAddr) {
    initialAddressData.value = matchedAddr
  } else {
    showToast('未找到对应的地址信息')
  }
}

onMounted(async () => {
  try {
    // 如果是编辑模式，初始化编辑数据
    await initEditModeData()
  } catch (error) {
    console.error('页面初始化失败:', error)
    showToast('页面初始化失败')
  }
})
</script>

<style scoped lang="less">
.address-form {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F8F9FA 0%, #FFFFFF 100%);

  &__header {
    padding: 24px 16px 16px;
    text-align: center;
    background: @bg-color-white;
    border-bottom: 1px solid #F0F2F5;
  }

  &__title {
    font-size: @font-size-20;
    font-weight: @font-weight-600;
    color: @text-color-primary;
    margin: 0 0 8px 0;
    letter-spacing: 0.3px;
  }

  &__subtitle {
    font-size: @font-size-13;
    color: @text-color-tertiary;
    margin: 0;
    letter-spacing: 0.1px;
  }

  &__content {
    flex: 1;
    padding: 10px;
    background-color: @bg-color-gray;
  }
}
</style>
