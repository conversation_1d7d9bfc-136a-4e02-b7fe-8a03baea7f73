<template>
  <div class="order-address-edit">
    <!-- 提示信息 -->
    <div class="tip-box">
      <div class="tip-content">
        提示：发货前订单仅支持修改一次，修改后可能会影响物流时效。若商品因换地址、已发货、运费变更等原因导致修改失败，请您谅解。
      </div>
    </div>

    <!-- 当前地址 -->
    <div class="current-address">
      <div class="title">原地址</div>
      <div class="address-info">
        <div class="address-info-left">
          <div class="contact">{{ addressInfo.recName }} {{ addressInfo.recPhone }}</div>
          <div class="edit-op" @click="editAddress">
            <div class="edit-btn">修改原地址</div>
            <img class="edit-icon" src="./assets/edit.png" alt=""/>
          </div>
        </div>
        <div class="address-info-right">
          <div class="address">{{ addressInfo.recAddress }}</div>
        </div>
      </div>
    </div>

    <!-- 选择新的收货地址 -->
    <div class="select-address">
      <div class="title-row">
        <div class="title">选择新收货地址</div>
        <div class="add-address" @click="addNewAddress">+ 新增地址</div>
      </div>
    </div>

    <div class="address-list" ref="addressListRef">
      <template v-if="addressList.length > 0">
        <div
          v-for="(item, index) in addressList"
          :key="item.addressId || index"
          class="address-item"
          :class="{ 'cur': selectedIndex === index }"
          @click="selectAddress(index)"
        >
          <div class="address-content">
            <div class="contact">{{ item.recName }} {{ item.recPhone }}</div>
            <div class="address">
              <span>{{ item.provinceName }}&nbsp;</span>
              <span>{{ item.cityName }}&nbsp;</span>
              <span>{{ item.countyName }}&nbsp;</span>
              <span>{{ item.townName }}&nbsp;</span>
              <span>{{ item.addrDetail }}</span>
            </div>
          </div>
          <div class="radio-btn">
            <img :src="selectedIndex === index ? checkImg : uncheckImg" alt="选择状态" />
          </div>
        </div>
      </template>
      <div v-else class="empty-address">暂无收货地址，请添加</div>
    </div>

    <!-- 确认修改按钮 -->
    <div class="confirm-btn-wrapper">
      <div
        class="confirm-btn"
        :class="{ 'disabled': isLoading }"
        @click="confirmChange"
      >
        {{ isLoading ? '提交中...' : '确认修改' }}
      </div>
    </div>
    <!-- 地址选择 -->
    <PopupAddrEdit v-model="showLocationSelector" :addressInfo="addressInfo" @save="saveAddress"/>
  </div>
</template>

<script>
import checkImg from '@/views/WoMall/address/assets/check.png'
import uncheckImg from '@/views/WoMall/address/assets/uncheck.png'
import { modifyOrderAddress } from '@/api/order'
import PopupAddrEdit from '@/components/Address/PopupAddrEdit.vue'

export default {
  name: 'OrderAddressEdit',
  components: { PopupAddrEdit },
  data () {
    return {
      checkImg,
      uncheckImg,
      addressInfo: {
        recName: '',
        recPhone: '',
        recAddress: ''
      },
      orderId: '',
      selectedIndex: '',
      isLoading: false,
      showLocationSelector: false
    }
  },
  computed: {
    addressList () {
      return this.$store.state.user.addressList || []
    }
  },
  created () {
    this.initRouteParams()
    this.fetchAddressList()
  },
  methods: {
    // 初始化路由参数
    initRouteParams () {
      // 获取地址信息
      const addressInfoStr = this.$route.query.addressInfo
      if (addressInfoStr) {
        try {
          this.addressInfo = JSON.parse(decodeURIComponent(addressInfoStr))
          console.warn(12313231, this.addressInfo)
        } catch (e) {
          console.error('解析地址信息失败', e)
          this.$toast('地址信息解析失败')
        }
      }
      // 获取订单ID
      this.orderId = this.$route.query.orderId || ''
      if (!this.orderId) {
        console.error('缺少订单ID参数')
        this.$toast('缺少订单ID参数')
      }
    },
    // 获取地址列表
    fetchAddressList () {
      this.$store.dispatch('user/queryAddrList', { force: true })
        .then(() => {
          this.$nextTick(() => {
            this.scrollToSelectedAddress()
          })
        })
        .catch(err => {
          console.error('获取地址列表失败', err)
          this.$toast('获取地址列表失败，请重试')
        })
    },
    // 滚动到选中的地址
    scrollToSelectedAddress () {
      const addressListEl = this.$refs.addressListRef
      if (!addressListEl) return

      const selectedEl = addressListEl.querySelector('.cur')
      if (selectedEl) {
        selectedEl.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
    },
    // 选择地址
    selectAddress (index) {
      if (this.isLoading) return
      this.selectedIndex = index
    },
    editAddress () {
      this.showLocationSelector = true
    },
    // 添加新地址
    addNewAddress () {
      if (this.isLoading) return
      // 保存当前页面状态，以便返回时恢复
      this.$router.push({
        path: '/addr/add',
        query: { redirect: this.$route.fullPath }
      })
    },
    async saveAddress (addressInfo) {
      // 防止重复提交
      if (this.isLoading) return
      this.isLoading = true
      this.$toast.queueLoading()

      try {
        const orderAddressRecord = JSON.stringify({
          orderId: this.orderId,
          newAddress: {
            ...this.addressInfo,
            ...addressInfo
          }
        })

        // 发送请求
        const [err] = await modifyOrderAddress(orderAddressRecord)
        this.$toast.queueClear()

        // 处理响应
        if (err) {
          this.handleError(err)
        } else {
          this.handleSuccess()
        }
      } catch (e) {
        console.error('修改地址异常', e)
        this.$toast('网络异常，请稍后重试')
      } finally {
        this.isLoading = false
      }
    },
    // 确认修改地址
    async confirmChange () {
      // 验证选择的地址
      if (this.selectedIndex === '' || this.selectedIndex < 0 || this.selectedIndex >= this.addressList.length) {
        this.$toast('请选择新收货地址')
        return
      }

      // 防止重复提交
      if (this.isLoading) return
      this.isLoading = true
      this.$toast.queueLoading()

      try {
        // 准备请求数据
        const selectedAddress = this.addressList[this.selectedIndex]
        const orderAddressRecord = JSON.stringify({
          orderId: this.orderId,
          newAddress: selectedAddress
        })

        // 发送请求
        const [err] = await modifyOrderAddress(orderAddressRecord)
        this.$toast.queueClear()

        // 处理响应
        if (err) {
          this.handleError(err)
        } else {
          this.handleSuccess()
        }
      } catch (e) {
        console.error('修改地址异常', e)
        this.$toast('网络异常，请稍后重试')
      } finally {
        this.isLoading = false
      }
    },
    // 处理错误
    handleError (err) {
      if (err.code === '9999') {
        // 简单错误直接提示
        this.$toast(err.msg || '地址修改失败')
      } else {
        // 需要用户确认的错误使用弹窗
        this.$alert2({
          message: err.msg || '地址修改失败',
          confirmButtonText: '确定'
        })
      }
    },
    // 处理成功
    handleSuccess () {
      this.$alert2({
        message: '修改信息已提交，修改成功后将更新订单信息。',
        confirmButtonText: '确定'
      }).then(() => {
        this.$router.back()
      })
    }
  }
}
</script>

<style scoped lang="less">
.order-address-edit {
  background-color: #f5f5f5;
  height: 100vh;
  padding-bottom: 50px;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .tip-box {
    padding: 9px 20px;
    background-color: #FFF5E6;

    .tip-content {
      color: #FF6E00;
      font-size: 11px;
      line-height: 18px;
    }
  }

  .current-address {
    padding: 15px 20px;
    background-color: #FFFFFF;

    .title {
      font-size: 15px;
      font-weight: bold;
      color: #171E24;
      margin-bottom: 10px;
    }

    .address-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .address-info-left {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        .contact {
          font-size: 14px;
          color: #333333;
          margin-bottom: 5px;
        }

        .edit-op {
          flex-shrink: 0;
          display: flex;
          align-items: center;
          font-size: 13px;
          color: #666666;
          .edit-icon {
            margin-left: 5px;
            width: 15px;
            height: 15px;
          }
        }
      }
      .address-info-right {
        .address {
          font-size: 13px;
          color: #666666;
          line-height: 18px;
          display: -webkit-box; /* 设置为WebKit内核的弹性盒子模型 */
          -webkit-box-orient: vertical; /* 垂直排列 */
          -webkit-line-clamp: 2; /* 限制显示两行 */
          overflow: hidden; /* 隐藏超出范围的内容 */
          text-overflow: ellipsis; /* 使用省略号 */
        }
      }
    }
  }

  .select-address {
    margin-top: 10px;
    padding: 17px 20px;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;

    .title-row {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 14px;
        font-weight: bold;
        color: #171E24;
      }

      .add-address {
        font-size: 14px;
        color: #FF831F;
      }
    }
  }

  .address-list {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 20px;
    background-color: #FFFFFF;

    .empty-address {
      padding: 30px 0;
      text-align: center;
      color: #999;
      font-size: 14px;
    }

    .address-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #EEEEEE;

      &:last-child {
        border-bottom: none;
      }

      .address-content {
        flex: 1;

        .contact {
          font-size: 13px;
          color: #333333;
          margin-bottom: 5px;
        }

        .address {
          font-size: 13px;
          color: #666666;
          line-height: 18px;
        }
      }

      .radio-btn {
        margin-left: 10px;

        img {
          width: 18px;
          height: 18px;
          display: block;
        }
      }
    }
  }

  .confirm-btn-wrapper {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #FFFFFF;
    padding: 10px 15px;
    box-sizing: border-box;
    z-index: 10;
  }

  .confirm-btn {
    width: 100%;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background-image: linear-gradient(96deg, #FFA033 0%, #FF6D33 100%);
    color: #FFFFFF;
    font-size: 17px;
    font-weight: 500;
    border-radius: 25px;
    cursor: pointer;

    &.disabled {
      opacity: 0.6;
      pointer-events: none;
    }
  }
}
</style>
